import React, { useState, useEffect, useMemo } from 'react';
import LanguageList from 'language-list';
import AlbumCoverUpload from '@/components/AlbumCoverUpload';
import { api } from '@/services';
import { useAuthStore } from '@/store/authStore';
import { EditFilled } from '@ant-design/icons';
import EditButton from '@/components/Profile/coms/EditButton';

import type {
  AudioFormat,
  ReleaseOption,
  Genre,
  SubmitTrackRequest,
} from '@/types/api';
import MusicUpload from '@/components/MusicUpload';
const languageListInstance = new LanguageList();

// console.log(languageListInstance.getLanguageCodes());
// console.log(languageListInstance.getData());
import {
  LANGUAGE_OPTIONS,
  type LanguageOption,
} from '@/utils/language-options';
import {
  Form,
  message,
  Input,
  Button,
  DatePicker,
  Select,
  Checkbox,
  ConfigProvider,
} from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import type * as dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;

/**
 * 扩展提交音乐作品表单类型，支持 dayjs 类型的字段
 */
interface SubmitTrackRequestForm
  extends Omit<
    SubmitTrackRequest,
    | 'copyrightYear'
    | 'originalReleaseDate'
    | 'streetDate'
    | 'phonogramCopyrightYear'
  > {
  copyrightYear: dayjs.Dayjs;
  originalReleaseDate: dayjs.Dayjs;
  streetDate: dayjs.Dayjs;
  phonogramCopyrightYear: dayjs.Dayjs;
}

const SubmitMusic: React.FC = () => {
  const { t, language } = useLanguage();
  const [form] = Form.useForm<SubmitTrackRequestForm>();
  const [stageNameForm] = Form.useForm();
  const [bioForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [coverArtUrl, setCoverArtUrl] = useState<string>('');
  const [audioFormats, setAudioFormats] = useState<AudioFormat[]>([]);
  const [releaseOptions, setReleaseOptions] = useState<ReleaseOption[]>([]);
  const [genres, setGenres] = useState<Genre[]>([]);
  const [mediaUrls, setMediaUrls] = useState<string>('');
  const { isArtist, profile, fetchUserProfile } = useAuthStore();
  // 编辑状态管理
  const [editingFields, setEditingFields] = useState<Record<string, boolean>>(
    {}
  );
  // 用于标记是否需要在编辑状态变化后进行校验
  const [needsValidationAfterEdit, setNeedsValidationAfterEdit] = useState<{
    stageName?: boolean;
    bio?: boolean;
  }>({});
  useEffect(() => {
    setEditingFields({
      stageName: !profile?.stageName,
      bio: !profile?.bio,
    });
  }, [profile]);

  // 监听编辑状态变化，在状态变为编辑模式后进行校验
  useEffect(() => {
    if (needsValidationAfterEdit.stageName && editingFields.stageName) {
      // 延迟一下确保表单已经渲染
      setTimeout(() => {
        stageNameForm.validateFields();
        setNeedsValidationAfterEdit(prev => ({ ...prev, stageName: false }));
      }, 0);
    }
  }, [
    editingFields.stageName,
    needsValidationAfterEdit.stageName,
    stageNameForm,
  ]);

  useEffect(() => {
    if (needsValidationAfterEdit.bio && editingFields.bio) {
      // 延迟一下确保表单已经渲染
      setTimeout(() => {
        bioForm.validateFields();
        setNeedsValidationAfterEdit(prev => ({ ...prev, bio: false }));
      }, 0);
    }
  }, [editingFields.bio, needsValidationAfterEdit.bio, bioForm]);
  // 校验 stageName 和 bio
  const validStageAndbio = async (): Promise<boolean> => {
    try {
      // 校验 stageName：如果正在编辑则校验表单，否则检查 profile 中是否有值
      const validStageName = true;
      if (editingFields.stageName) {
        await stageNameForm.validateFields();
      } else if (!profile?.stageName) {
        // 设置需要校验的标记，然后切换到编辑状态
        setNeedsValidationAfterEdit(prev => ({ ...prev, stageName: true }));
        setEditingFields(prev => ({ ...prev, stageName: true }));
        message.error('请先填写艺人名称');
        return false;
      }

      // 校验 bio
      if (editingFields.bio) {
        await bioForm.validateFields();
      } else if (!profile?.bio) {
        // 设置需要校验的标记，然后切换到编辑状态
        setNeedsValidationAfterEdit(prev => ({ ...prev, bio: true }));
        setEditingFields(prev => ({ ...prev, bio: true }));
        message.error('请先填写艺人简介');
        return false;
      }

      return true;
    } catch (error) {
      console.error('校验失败:', error);
      message.error('请完善艺人信息');
      return false;
    }
  };
  // 开始编辑
  const startEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: true }));
    // 设置表单初始值
    if (fieldName === 'stageName') {
      stageNameForm.setFieldsValue({ stageName: profile?.stageName || '' });
    } else if (fieldName === 'bio') {
      bioForm.setFieldsValue({ bio: profile?.bio || '' });
    }
  };
  // 取消编辑
  const cancelEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: false }));
    // 重置对应的表单字段到初始值
    if (fieldName === 'stageName') {
      stageNameForm.resetFields();
    } else if (fieldName === 'bio') {
      bioForm.resetFields();
    }
  };

  // 保存 stageName
  const handleSaveStageName = async () => {
    try {
      await stageNameForm.validateFields();
      const stageName = stageNameForm.getFieldValue('stageName');
      const response = await api.user.updateProfile({ stageName });
      if (response.code === 200) {
        setEditingFields(prev => ({ ...prev, stageName: false }));
        message.success(t('common.saveSuccess'));
        await fetchUserProfile();
      } else {
        message.error(response.message || t('common.saveFailed'));
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error(t('common.saveFailed'));
    }
  };

  // 保存 bio
  const handleSaveBio = async () => {
    try {
      await bioForm.validateFields();
      const bio = bioForm.getFieldValue('bio');
      const response = await api.user.updateProfile({ bio });
      if (response.code === 200) {
        setEditingFields(prev => ({ ...prev, bio: false }));
        message.success(t('common.saveSuccess'));
        await fetchUserProfile();
      } else {
        message.error(response.message || t('common.saveFailed'));
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error(t('common.saveFailed'));
    }
  };

  const languageOptions = useMemo(() => {
    return LANGUAGE_OPTIONS.map(item => ({
      value: item.tag,
      label: language === 'zh' ? item.name_zh : item.name_en,
    }));
  }, [language]);

  const loadOptions = async () => {
    const [audioFormatsRes, releaseOptionsRes, genresRes] = await Promise.all([
      api.music.getDefaultAudioFormats(),
      api.music.getDefaultReleaseOptions(),
      api.music.getDefaultGenres(),
    ]);
    if (audioFormatsRes.code === 200) {
      setAudioFormats(audioFormatsRes.body);
    }
    if (releaseOptionsRes.code === 200) {
      setReleaseOptions(releaseOptionsRes.body);
    }
    if (genresRes.code === 200) {
      setGenres(genresRes.body);
    }
    // console.log(audioFormats, releaseOptions, genres);
  };

  useEffect(() => {
    loadOptions();
  }, []);
  const handleSubmit = async (values: SubmitTrackRequestForm) => {
    // 先校验 stageName 和 bio
    const isProfileValid = await validStageAndbio();
    if (!isProfileValid) {
      return;
    }

    if (!coverArtUrl) {
      message.error(t('submitMusic.placeholders.coverArtRequired'));
      return;
    }

    setLoading(true);
    try {
      console.log('values', values.originalReleaseDate);
      // 确保包含封面 URL
      const submitData: SubmitTrackRequest = {
        ...values,
        originalReleaseDate: values.originalReleaseDate.valueOf(),
        streetDate: values.streetDate.valueOf(),
        copyrightYear: values.copyrightYear.year().toString(),
        phonogramCopyrightYear: values.phonogramCopyrightYear.year().toString(),
        phonogramCopyright: values.phonogramCopyright,
        coverArtUrl: coverArtUrl,
      };
      console.log('提交的数据:', submitData);
      const res = await api.music.submitTrack(submitData);
      if (res.code === 200) {
        message.success(t('common.submitSuccess'));
        form.resetFields();
      } else {
        message.error(t('common.submitFailed'));
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error(t('common.submitFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
  };

  // 处理专辑封面上传成功
  const handleCoverUploadSuccess = async (url: string) => {
    try {
      setCoverArtUrl(url);
      // 同时更新表单字段
      form.setFieldValue('coverArtUrl', url);
      message.success(t('common.saveSuccess'));
    } catch (error) {
      console.error('专辑封面上传失败:', error);
      message.error(t('common.saveFailed'));
    }
  };

  // 自定义专辑封面上传请求
  const customCoverUploadRequest = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      // TODO 现在使用上传头像的API处理专辑封面
      const response = await api.user.uploadAvatar(file);
      onSuccess({ url: response.url });
    } catch (error) {
      console.error('专辑封面上传失败:', error);
      onError(error);
    }
  };

  // 处理附加媒体文件上传成功
  const handleAdditionalUploadSuccess = async (url: string) => {
    setMediaUrls(url);
    form.setFieldValue('mediaUrls', url);
  };

  const rules = {
    title: [{ required: true }],
    labelName: [{ required: true }],
    primaryLanguage: [{ required: true }],
    primaryGenre: [{ required: true }],
    secondaryGenre: [{ required: true }],
    originalReleaseDate: [{ required: true }],
    streetDate: [{ required: true }],
    trackIntro: [{ required: true }],
    copyrightName: [{ required: true }],
    copyrightYear: [{ required: true }],
    phonogramCopyrightYear: [{ required: true }],
    phonogramCopyright: [{ required: true }],
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            labelFontSize: 12,
            labelColor: 'var(--color-label)',
          },
        },
      }}
    >
      <div className="min-h-screen bg-[#0d0d0d] relative min-w-1000px">
        <div className="pl-8 pr-8 pt-6 max-w-1800px mr-auto">
          {/* 艺人信息*/}
          <div className="mb-10">
            <h2 className="text-white text-[22px] font-bold mb-6 ">
              {t('common.artistInfo')}
            </h2>

            <div className="mb-6 flex items-center">
              <div className="text-label text-[18px] font-bold mr-30px ">
                {t('submitMusic.primaryArtist')}:
              </div>
              <div className="w-[854px] lg:w-[80%]">
                {editingFields.stageName ? (
                  <Form form={stageNameForm} onFinish={handleSaveStageName}>
                    <Form.Item
                      name="stageName"
                      rules={[
                        { required: true, message: t('common.saveFailed') },
                      ]}
                    >
                      <Input className="s-submit-input" />
                    </Form.Item>
                    <EditButton
                      className="pl-114px"
                      htmlType="submit"
                      onCancel={() => cancelEdit('stageName')}
                      onSave={() => stageNameForm.submit()}
                    />
                  </Form>
                ) : (
                  <>
                    <span>{profile?.stageName}</span>
                    <EditFilled
                      className="ml-10px cursor-pointer hover:text-primary"
                      onClick={() => startEdit('stageName')}
                    />
                  </>
                )}
              </div>
            </div>

            <div className="mb-6">
              <div className="text-label text-[18px] font-bold mb-5 ">
                {t('submitMusic.artistBio')}:
              </div>

              {editingFields.bio ? (
                <Form form={bioForm} onFinish={handleSaveBio}>
                  <Form.Item
                    name="bio"
                    rules={[
                      { required: true, message: t('common.saveFailed') },
                    ]}
                  >
                    <TextArea className="s-submit-input" maxLength={400} />
                  </Form.Item>
                  <EditButton
                    className="pl-114px"
                    htmlType="submit"
                    onCancel={() => cancelEdit('bio')}
                    onSave={() => bioForm.submit()}
                  />
                </Form>
              ) : (
                <>
                  <span>{profile?.bio}</span>
                  <EditFilled
                    className="ml-10px cursor-pointer hover:text-primary"
                    onClick={() => startEdit('bio')}
                  />
                </>
              )}
            </div>
          </div>
          <Form
            requiredMark={false}
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            onFinishFailed={() => validStageAndbio()}
            className="max-w-none"
          >
            {/* Track Info Section */}
            <div className="mb-10">
              <h2 className="text-white text-[22px] font-bold mb-8 ">
                {t('submitMusic.trackInfo')}
              </h2>

              <div className="flex gap-8 mb-8">
                <div className="w-[340px] h-[340px]">
                  <AlbumCoverUpload
                    coverArtUrl={coverArtUrl}
                    size={340}
                    onUploadSuccess={handleCoverUploadSuccess}
                    customRequest={customCoverUploadRequest}
                  />
                </div>

                <div className="flex-1">
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    layout="horizontal"
                    colon={false}
                    label={
                      <div className="w-60px">{t('submitMusic.title')}:</div>
                    }
                    messageVariables={{
                      label: `${t('submitMusic.title')}`,
                    }}
                    rules={rules.title}
                    name="title"
                  >
                    <Input
                      className="s-submit-input"
                      placeholder={t('submitMusic.placeholders.title')}
                    />
                  </Form.Item>
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    layout="horizontal"
                    colon={false}
                    label={
                      <div className="w-60px">
                        {t('submitMusic.labelName')}:
                      </div>
                    }
                    messageVariables={{
                      label: `${t('submitMusic.labelName')}`,
                    }}
                    rules={rules.labelName}
                    name="labelName"
                  >
                    <Input
                      className="s-submit-input"
                      placeholder={t('submitMusic.placeholders.labelName')}
                    />
                  </Form.Item>
                  <Form.Item
                    labelAlign="left"
                    label={`${t('submitMusic.trackIntro')}:`}
                    rules={rules.trackIntro}
                    name="trackIntro"
                    messageVariables={{
                      label: `${t('submitMusic.trackIntro2')}`,
                    }}
                  >
                    <TextArea
                      className="s-submit-input "
                      placeholder={t('submitMusic.placeholders.trackIntro')}
                      style={{ borderRadius: 0 }}
                    />
                  </Form.Item>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-8 mb-6">
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.albumNameOptional')}:
                    </div>
                  }
                  name="albumName"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.title')}
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  rules={rules.primaryLanguage}
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.primaryLanguage')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.primaryLanguage')}`,
                  }}
                  name="primaryLanguage"
                >
                  <Select
                    className="s-submit-select !h-42px"
                    placeholder={t('submitMusic.selectLanguage')}
                  >
                    {languageOptions.map(item => (
                      <Option value={item.value} key={item.value}>
                        {item.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.UPCOptional')}:
                    </div>
                  }
                  name="upc"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.upc')}
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.ISRCOptional')}:
                    </div>
                  }
                  name="isrc"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.isrc')}
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.primaryGenre')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.primaryGenre')}`,
                  }}
                  rules={rules.primaryGenre}
                  name="primaryGenre"
                >
                  <Select className="s-submit-select !h-42px">
                    {genres.map(genre => (
                      <Option value={genre.code} key={genre.code}>
                        {genre.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  label={
                    <div className="text-label  text-[18px] font-bold">
                      {t('submitMusic.secondaryGenre')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.secondaryGenre')}`,
                  }}
                  rules={rules.secondaryGenre}
                  name="secondaryGenre"
                >
                  <Select className="s-submit-select !h-42px">
                    {genres.map(genre => (
                      <Option value={genre.code} key={genre.code}>
                        {genre.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  rules={rules.originalReleaseDate}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      {t('submitMusic.originalReleaseDate')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.originalReleaseDate')}`,
                  }}
                  name="originalReleaseDate"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    format="DD/MM/YYYY"
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  rules={rules.streetDate}
                  colon={false}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      {t('submitMusic.streetDate')}:
                    </div>
                  }
                  messageVariables={{
                    label: `${t('submitMusic.streetDate')}`,
                  }}
                  name="streetDate"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    format="DD/MM/YYYY"
                  />
                </Form.Item>
              </div>
            </div>
            {/* 版权信息Section */}
            <div className="mb-10">
              <h2 className="text-white text-[22px] font-bold mb-8 ">
                {t('submitMusic.copyrightInfo')}
              </h2>
              <div className="grid grid-cols-3 gap-8 mb-6">
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `©${t('submitMusic.year')}`,
                  }}
                  rules={rules.copyrightYear}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ©{t('submitMusic.year')}:
                    </div>
                  }
                  name="copyrightYear"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    placeholder={t('submitMusic.placeholders.datePicker')}
                    picker="year"
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `©${t('submitMusic.name')}`,
                  }}
                  rules={rules.copyrightName}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ©{t('submitMusic.name')}:
                    </div>
                  }
                  name="copyrightName"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.name')}
                  />
                </Form.Item>
                <Form.Item
                  className="col-start-1 s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `℗${t('submitMusic.year')}`,
                  }}
                  rules={rules.phonogramCopyrightYear}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ℗{t('submitMusic.year')}:
                    </div>
                  }
                  name="phonogramCopyrightYear"
                >
                  <DatePicker
                    className="s-submit-datepicker !h-42px"
                    placeholder={t('submitMusic.placeholders.datePicker')}
                    picker="year"
                  />
                </Form.Item>
                <Form.Item
                  className="s-submit-input-label"
                  labelAlign="left"
                  colon={false}
                  messageVariables={{
                    label: `℗${t('submitMusic.name')}`,
                  }}
                  rules={rules.phonogramCopyright}
                  label={
                    <div className="text-label text-[18px] font-bold">
                      ℗{t('submitMusic.name')}:
                    </div>
                  }
                  name="phonogramCopyright"
                >
                  <Input
                    className="s-submit-input"
                    placeholder={t('submitMusic.placeholders.name')}
                  />
                </Form.Item>
              </div>
            </div>

            <div className="mb-10 flex ">
              {/* 音频格式Section */}
              <div>
                <h2 className="text-white text-[22px] font-bold mb-8 ">
                  {t('submitMusic.audioFormatOptional')}
                </h2>

                <div className="w-[454px]">
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    colon={false}
                  >
                    <Checkbox.Group className="s-submit-checkbox">
                      <div className="flex flex-col gap-24px">
                        {audioFormats.map(audioFormat => (
                          <Checkbox value={audioFormat.code}>
                            {audioFormat.name}
                          </Checkbox>
                        ))}
                      </div>
                    </Checkbox.Group>
                  </Form.Item>
                </div>
              </div>
              <div>
                <h2 className="text-white text-[22px] font-bold mb-8 ">
                  {t('submitMusic.releaseOptions')}
                </h2>
                <div className="w-[454px]">
                  <Form.Item
                    className="s-submit-input-label"
                    labelAlign="left"
                    colon={false}
                  >
                    <Checkbox.Group className="s-submit-checkbox">
                      <div className="flex flex-col gap-24px">
                        {releaseOptions.map(releaseOption => (
                          <Checkbox value={releaseOption.code}>
                            {releaseOption.name}
                          </Checkbox>
                        ))}
                      </div>
                    </Checkbox.Group>
                  </Form.Item>
                </div>
              </div>
            </div>

            <div className="mb-16 flex items-center gap-40px">
              <h2 className="text-white text-[22px] font-bold mb-8 ">
                {t('submitMusic.additionalMediaFiles')}
              </h2>

              <MusicUpload onUploadSuccess={handleAdditionalUploadSuccess} />
            </div>

            <div className="flex justify-center gap-6 mb-60px">
              <Button
                size="large"
                className="w-[496px] h-[63px] bg-transparent rounded-6px text-primary text-[16px] font-bold border border-primary hover:opacity-45 hover:!bg-transparent  transition-colors"
                onClick={handleCancel}
              >
                {t('submitMusic.form.cancel')}
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={loading}
                className="w-[496px] h-[63px] bg-primary text-black text-[16px] font-bold border-none rounded-6px hover:opacity-45 transition-colors"
              >
                {t('submitMusic.form.submit')}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default SubmitMusic;
